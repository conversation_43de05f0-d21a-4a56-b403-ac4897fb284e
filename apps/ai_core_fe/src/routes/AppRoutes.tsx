import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import SignInPage from '../pages/SignInPage';
import DashboardLayout from '../layouts/DashboardLayout';
import ReportsPage from '../pages/ReportsPage';
import UserSettingsPage from '../pages/UserSettingsPage';
import HelpPage from '../pages/HelpPage';
import UsersPage from '../pages/UsersPage';
import SqlDebugPage from '../pages/SqlDebugPage';

// Protected route component
const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <Navigate to="/signin" replace />;
  }
  
  return children;
};

const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/signin" element={<SignInPage />} />
      <Route path="/" element={
        <ProtectedRoute>
          <DashboardLayout />
        </ProtectedRoute>
      }>
        <Route index element={<Navigate to="/reports" replace />} />
        <Route path="reports">
          <Route index element={<ReportsPage />} />
          <Route path=":transcription_request_id" element={<ReportsPage />} />
        </Route>
        <Route path="sql" element={<SqlDebugPage />} />
        <Route path="settings" element={<UserSettingsPage />} />
        <Route path="help" element={<HelpPage />} />
        <Route path="users" element={<UsersPage />} />
      </Route>
      <Route path="*" element={<Navigate to="/signin" replace />} />
    </Routes>
  );
};

export default AppRoutes;