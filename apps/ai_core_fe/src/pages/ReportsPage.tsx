import React, { useState, useEffect, useMemo, useRef } from 'react';
import { 
  <PERSON>, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  CardHeader, 
  Tabs, 
  Tab, 
  Chip, 
  Divider, 
  Button, 
  alpha,
  Stack,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField
} from '@mui/material';
import { 
  TrendingUp, 
  TrendingDown,
  Download,
  Calendar
} from 'lucide-react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Line } from 'react-chartjs-2';
import { apiClient } from '../apiClient';
import { useAuth } from '../context/AuthContext';
import { useParams } from 'react-router-dom';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement);

// Define types for fetched data
interface AnsweredQuestion {
  question_code: string;
  question_text: string;
  answer_context: string[];
  answer_text: string[];
}

interface TranscriptionRating {
  id: string;
  request_id: string;
  question_code: string;
  answer_code: 'Correct' | 'Incorrect' | 'Hallucination';
  comment: string;
  admin_id: string;
}

interface CommentAnnotation {
  id: string;
  request_id: string;
  comment_idx: number;
  annotation: string;
  admin_id: string;
}

interface TranscriptionRun {
  id: string;
  user_id: string;
  visit_id: string;
  client_id: string;
  assessment_id: string;
  company_id: string;
  transcribe_type: string;
  audio_files: string[];
  question_files: string[];
  mode: string;
  status: string;
  started: number;
  completed: number;
  exception: string | null;
  answer_files: string[];
  transcription_files: string[];
  conversation_files: string[];
  total_questions: number;
  answered_questions_cnt: number;
  answered_questions: AnsweredQuestion[];
  total_time: number;
  transcription_time: number;
  transcription_service_type: string;
  transcription_service_model: string;
  ai_service_type: string;
  ai_service_model: string;
  ai_temperature: number;
  ai_prompt: string | null;
  ai_embeddings: string[];
  audio_buffer_size: number;
  version: string;
  commit: string | null;
}

// Fetch transcription runs from API
/**
 * ReportsPage component displays a list of transcription runs and provides detailed information.
 * 
 * URL-based navigation features:
 * - Direct linking to a specific transcription run via /reports/:transcription_request_id
 * - Automatic scrolling to the specified run when accessed via direct URL
 * - Ability to copy shareable links to specific transcription runs
 */
const ReportsPage = () => {
  const { user } = useAuth();
  const { transcription_request_id } = useParams<{ transcription_request_id?: string }>();
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('30');
  // Use a more specific type for transcription runs if available, otherwise fallback to unknown[]
  const [newTranscriptionRuns, setNewTranscriptionRuns] = useState<TranscriptionRun[]>([]);
  const [jsonDataMap, setJsonDataMap] = useState<Record<string, unknown>>({});
  const [jsonLoadingMap, setJsonLoadingMap] = useState<Record<string, boolean>>({});
  // Create refs for transcription rows
  const transcriptionRefs = useRef<{ [key: string]: HTMLTableRowElement | null }>({});
  const [ratingsMap, setRatingsMap] = useState<Map<string, TranscriptionRating>>(new Map());
  const [commentAnnotationsMap, setCommentAnnotationsMap] = useState<Map<string, CommentAnnotation>>(new Map());
  const [annotationDialog, setAnnotationDialog] = useState<{
    open: boolean;
    run: TranscriptionRun | null;
    commentIdx: number | null;
    annotation: string;
    existing: CommentAnnotation | null;
  }>({
    open: false,
    run: null,
    commentIdx: null,
    annotation: '',
    existing: null,
  });
  
  // Function to scroll to a specific transcription
  const scrollToTranscription = (runId: string) => {
    const element = transcriptionRefs.current[runId];
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // Add highlight effect
      element.style.backgroundColor = '#fff9c4';
      setTimeout(() => {
        element.style.transition = 'background-color 2s ease';
        element.style.backgroundColor = '';
      }, 100);
    }
  };
  
  // Rating dialog state
  const [ratingDialog, setRatingDialog] = useState<{
    open: boolean;
    type: 'Correct' | 'Incorrect' | 'Hallucination' | null;
    run: TranscriptionRun | null;
    question: AnsweredQuestion | null;
  }>({
    open: false,
    type: null,
    run: null,
    question: null,
  });
  const [ratingComment, setRatingComment] = useState('');
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch transcription runs
        const data = await apiClient<TranscriptionRun[]>(
          'transcription-request-list',
          { method: 'GET' },
          true
        );
        setNewTranscriptionRuns(data);

        // Fetch existing ratings
        const ratings = await apiClient<TranscriptionRating[]>(
          'transcription-rating-list',
          { method: 'GET' },
          true
        );
        
        // Create a map for quick lookup by rating ID (run.id + '-' + question.question_code)
        const ratingsMap = new Map<string, TranscriptionRating>();
        ratings.forEach(rating => {
          ratingsMap.set(rating.id, rating);
        });
        setRatingsMap(ratingsMap);
        
      } catch (error) {
        console.error('Failed to fetch data:', error);
        setNewTranscriptionRuns([]);
        setRatingsMap(new Map());
      }
    };
    fetchData();
  }, []);
  
  // Fetch CommentAnnotations on mount
  useEffect(() => {
    const fetchAnnotations = async () => {
      try {
        const annotations = await apiClient<CommentAnnotation[]>(
          'comment-annotations-list',
          { method: 'GET' },
          true
        );
        const map = new Map<string, CommentAnnotation>();
        annotations.forEach(a => {
          map.set(`${a.request_id}|${a.comment_idx}`, a);
        });
        setCommentAnnotationsMap(map);
      } catch (error) {
        setCommentAnnotationsMap(new Map());
      }
    };
    fetchAnnotations();
  }, []);
  
  // Scroll to the specified transcription request if an ID is provided in the URL
  useEffect(() => {
    if (transcription_request_id) {
      // Small delay to ensure the DOM is ready
      setTimeout(() => {
        scrollToTranscription(transcription_request_id);
      }, 300);
    }
  }, [transcription_request_id, newTranscriptionRuns]);
  
  // Sort newTranscriptionRuns by 'started' descending
  const sortedRuns = [...newTranscriptionRuns].sort((a, b) => b.started - a.started);

  // For chart: sort by 'started' ascending (earliest to latest)
  const chartRuns = [...newTranscriptionRuns].sort((a, b) => a.started - b.started);

  // Summary card component
  interface StatCardProps {
    title: string;
    value: string;
    change: number;
    subtitle: string;
    icon?: React.ReactNode;
  }

  const StatCard = ({ title, value, change, subtitle, icon }: StatCardProps) => {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent sx={{ p: 2.5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Typography variant="subtitle2" color="text.secondary">
              {title}
            </Typography>
            {icon && (
              <Box 
                sx={{ 
                  p: 1, 
                  borderRadius: 1, 
                  backgroundColor: alpha('#6366F1', 0.1),
                  color: 'primary.main'
                }}
              >
                {icon}
              </Box>
            )}
          </Box>
          <Typography variant="h4" component="div" sx={{ mb: 0.5, fontWeight: 600 }}>
            {value}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip
              icon={change >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
              label={`${Math.abs(change)}%`}
              size="small"
              color={change >= 0 ? 'success' : 'error'}
              sx={{ 
                height: 22,
                '& .MuiChip-label': { px: 1 },
                backgroundColor: change >= 0 ? alpha('#10B981', 0.1) : alpha('#EF4444', 0.1),
                color: change >= 0 ? 'success.dark' : 'error.dark',
                fontWeight: 500,
              }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
              {subtitle}
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  };
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  const handleTimeRangeChange = (event: SelectChangeEvent) => {
    setTimeRange(event.target.value);
  };

  // Handler for per-question correct/hallucination/incorrect - opens dialog
  const handleProcessQuestion = (
    type: 'Correct' | 'Incorrect' | 'Hallucination',
    run: TranscriptionRun,
    question: AnsweredQuestion
  ) => {
    setRatingDialog({
      open: true,
      type,
      run,
      question,
    });
    setRatingComment('');
  };

  // Handler for submitting the rating with comment
  const handleSubmitRating = async () => {
    if (!ratingDialog.type || !ratingDialog.run || !ratingDialog.question) return;

    try {
      const ratingId = ratingDialog.run.id + '-' + ratingDialog.question.question_code;
      
      // Prepare the rating payload
      const ratingPayload = {
        id: ratingId,
        request_id: ratingDialog.run.id,
        question_code: ratingDialog.question.question_code,
        answer_code: ratingDialog.type,
        comment: ratingComment,
        admin_id: user?.email || user?.id || "<EMAIL>"
      };

      // POST the rating to the API
      await apiClient('transcription-rating', {
        method: 'POST',
        body: JSON.stringify(ratingPayload),
        headers: { 'Content-Type': 'application/json' },
      }, true);

      // Update the ratingsMap with the new rating
      setRatingsMap(prev => new Map(prev.set(ratingId, ratingPayload as TranscriptionRating)));

      // Close dialog
      setRatingDialog({ open: false, type: null, run: null, question: null });
      setRatingComment('');

    } catch (error) {
      console.error('Failed to submit rating:', error);
      // Could add user notification here
    }
  };

  // Handler for canceling the rating dialog
  const handleCancelRating = () => {
    setRatingDialog({ open: false, type: null, run: null, question: null });
    setRatingComment('');
  };
  
  // Function to copy a transcription link to clipboard
  const copyTranscriptionLink = (runId: string) => {
    const url = `${window.location.origin}/reports/${runId}`;
    navigator.clipboard.writeText(url)
      .then(() => {
        // Could replace with a toast notification in a production app
        alert('Link copied to clipboard');
      })
      .catch(err => {
        console.error('Failed to copy link: ', err);
      });
  };
  
  // Handler for editing an existing rating
  const handleEditRating = (
    run: TranscriptionRun,
    question: AnsweredQuestion,
    existingRating: TranscriptionRating
  ) => {
    setRatingDialog({
      open: true,
      type: existingRating.answer_code,
      run,
      question,
    });
    setRatingComment(existingRating.comment || '');
  };
  
  // Calculate accuracy rate (average score across all runs)
  const accuracyRate = newTranscriptionRuns.length
    ? Math.round(
        newTranscriptionRuns.reduce(
          (sum, run) =>
            sum + (run.total_questions ? (run.answered_questions_cnt / run.total_questions) * 100 : 0),
          0
        ) / newTranscriptionRuns.length
      )
    : 0;

  // Calculate accuracy based on correct answers from rated questions
  const accuracyMetric = useMemo(() => {
    let correctCount = 0;
    let totalCount = 0;
    
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Correct') {
        correctCount++;
      }
    });
    
    const percentage = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;
    return { percentage, correctCount, totalCount };
  }, [ratingsMap]);

  // Calculate hallucination percentage from rated questions
  const hallucinationMetric = useMemo(() => {
    let hallucinationCount = 0;
    let totalCount = 0;
    
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Hallucination') {
        hallucinationCount++;
      }
    });
    
    const percentage = totalCount > 0 ? Math.round((hallucinationCount / totalCount) * 100) : 0;
    return { percentage, hallucinationCount, totalCount };
  }, [ratingsMap]);

  // Calculate incorrect percentage from rated questions
  const incorrectMetric = useMemo(() => {
    let incorrectCount = 0;
    let totalCount = 0;
    
    ratingsMap.forEach(rating => {
      totalCount++;
      if (rating.answer_code === 'Incorrect') {
        incorrectCount++;
      }
    });
    
    const percentage = totalCount > 0 ? Math.round((incorrectCount / totalCount) * 100) : 0;
    return { percentage, incorrectCount, totalCount };
  }, [ratingsMap]);
  
  // Memoize chart data and options for performance
  const chartData = useMemo(() => ({
    labels: chartRuns.map(run => new Date(run.started * 1000).toLocaleString()),
    datasets: [
      {
        label: 'Total Questions',
        data: chartRuns.map(run => run.total_questions),
        borderColor: '#6366F1',
        backgroundColor: 'rgba(99, 102, 241, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Answered Questions',
        data: chartRuns.map(run => run.answered_questions_cnt),
        borderColor: '#0EA5E9',
        backgroundColor: 'rgba(14, 165, 233, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Correct Questions',
        data: chartRuns.map(() => 0),
        borderColor: '#10B981',
        backgroundColor: 'rgba(16, 185, 129, 0.5)',
        tension: 0.3,
      },
      {
        label: 'Hallucinations',
        data: chartRuns.map(() => 0),
        borderColor: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.5)',
        tension: 0.3,
      },
    ]
  }), [chartRuns]);

  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    animation: { duration: 0 }, // Disable animation for instant rendering
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
    },
  }), []);
  
  const handleOpenAnnotationDialog = (run: TranscriptionRun, commentIdx: number, existing: CommentAnnotation | null) => {
    setAnnotationDialog({
      open: true,
      run,
      commentIdx,
      annotation: existing?.annotation || '',
      existing,
    });
  };

  const handleCloseAnnotationDialog = () => {
    setAnnotationDialog({ open: false, run: null, commentIdx: null, annotation: '', existing: null });
  };

  const handleSubmitAnnotation = async () => {
    if (!annotationDialog.run || annotationDialog.commentIdx == null) return;
    const id = `${annotationDialog.run.id}|${annotationDialog.commentIdx}`;
    const payload: CommentAnnotation = {
      id,
      request_id: annotationDialog.run.id,
      comment_idx: annotationDialog.commentIdx,
      annotation: annotationDialog.annotation,
      admin_id: user?.email || user?.id || '<EMAIL>',
    };
    try {
      await apiClient('comment-annotations', {
        method: 'POST',
        body: JSON.stringify(payload),
        headers: { 'Content-Type': 'application/json' },
      }, true);
      setCommentAnnotationsMap(prev => new Map(prev.set(id, payload)));
      handleCloseAnnotationDialog();
    } catch (error) {
      // Optionally show error
    }
  };
  
  const handleExportAssessment = (run: TranscriptionRun) => {
    // Export logic: download JSON for this run
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(run, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `assessment_${run.id}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };

  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Placeholder for import logic
    const file = event.target.files?.[0];
    if (!file) return;
    // Implement import logic as needed
    alert(`Import not implemented. Selected file: ${file.name}`);
  };
  
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 700 }}>
            AI Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            View and analyze AI insights from user trascriptions
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          {/*}
          <Button 
            variant="outlined" 
            startIcon={<Download size={18} />}
            sx={{ px: 2 }}
            component="label"
          >
            Import
            <input type="file" hidden onChange={handleImportFile} />
          </Button>
          */}
          <FormControl size="small" sx={{ minWidth: 140 }}>
            <InputLabel id="time-range-label">Time Range</InputLabel>
            <Select
              labelId="time-range-label"
              id="time-range"
              value={timeRange}
              label="Time Range"
              onChange={handleTimeRangeChange}
              startAdornment={<Calendar size={16} style={{ marginRight: 8, opacity: 0.7 }} />}
            >
              <MenuItem value="7">Last 7 days</MenuItem>
              <MenuItem value="30">Last 30 days</MenuItem>
              <MenuItem value="90">Last 90 days</MenuItem>
              <MenuItem value="365">Last year</MenuItem>
            </Select>
          </FormControl>
        </Stack>
      </Box>
      
      {/* Summary Stats */}
      <Grid container spacing={2} sx={{ mb: 1, flexWrap: 'nowrap' }}>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Total Transcriptions" 
            value={String(newTranscriptionRuns.length)} 
            change={5.3} 
            subtitle="vs last month" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Total Questions" 
            value={String(newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + (run.total_questions ?? 0), 0))} 
            change={8.1} 
            subtitle="vs last month" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Total Answered" 
            value={String(newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + (run.answered_questions_cnt ?? 0), 0))} 
            change={0} 
            subtitle="vs last month" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Response Time" 
            value={(() => {
              if (newTranscriptionRuns.length === 0) return '0s';
              const avg = Math.round(
                newTranscriptionRuns.reduce((sum, run: TranscriptionRun) => sum + ((run.completed ?? 0) - (run.started ?? 0)), 0) / newTranscriptionRuns.length
              );
              return avg + 's';
            })()} 
            change={-12.4} 
            subtitle="vs last month" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Answer Rate" 
            value={`${accuracyRate}%`} 
            change={2.7} 
            subtitle="vs last month" 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Accuracy" 
            value={`${accuracyMetric.percentage}%`} 
            change={0} 
            subtitle={`${accuracyMetric.correctCount}/${accuracyMetric.totalCount} rated`} 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Hallucinations" 
            value={`${hallucinationMetric.percentage}%`} 
            change={0} 
            subtitle={`${hallucinationMetric.hallucinationCount}/${hallucinationMetric.totalCount} rated`} 
          />
        </Grid>
        <Grid item xs={12} sm={6} md={1.5} lg={1.5} xl={1.5} zeroMinWidth>
          <StatCard 
            title="Incorrect" 
            value={`${incorrectMetric.percentage}%`} 
            change={0} 
            subtitle={`${incorrectMetric.incorrectCount}/${incorrectMetric.totalCount} rated`} 
          />
        </Grid>
      </Grid>
      
      {/* Tabs */}
      <Box sx={{ mb: 1 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange}
          TabIndicatorProps={{
            style: { height: 3, borderRadius: 3 }
          }}
          sx={{
            minHeight: 32,
            '& .MuiTab-root': {
              minHeight: 32,
              py: 0.5,
              fontWeight: 600,
              fontSize: '0.85rem',
              textTransform: 'none',
              '&.Mui-selected': {
                color: 'primary.main',
              },
            },
          }}
        >
          <Tab label="Overview" />
          <Tab label="Sentiment" />
          <Tab label="Topics" />
          <Tab label="Users" />
        </Tabs>
        <Divider />
      </Box>
      
      {/* Charts */}
      <Grid container spacing={2}>
        {tabValue === 0 && (
          <>
            <Grid item xs={12}>
              <Card sx={{ height: 280 }}>
                <CardHeader 
                  title="AI Metrics" 
                  subheader="Transcription run stats"
                  titleTypographyProps={{ variant: 'h6', fontWeight: 600 }}
                  subheaderTypographyProps={{ variant: 'body2' }}
                  sx={{ py: 1.5 }}
                />
                <Divider />
                <CardContent sx={{ height: 180, p: 0, pl: 1, pr: 1, pb: 0 }}>
                  <Box sx={{ width: '100%' }}>
                    <Line 
                      data={chartData}
                      options={chartOptions}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </>
        )}
        
        {tabValue === 1 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">Sentiment Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed sentiment analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {tabValue === 2 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">Topic Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed topic analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {tabValue === 3 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6">User Analysis</Typography>
                <Typography variant="body2" color="text.secondary">
                  This tab would display detailed user analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Transcription Runs Table */}
      <Box sx={{ mt: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h5" sx={{ fontWeight: 700 }}>
            Transcription Runs
          </Typography>
          {transcription_request_id && (
            <Box 
              sx={{ 
                display: 'flex', 
                alignItems: 'center', 
                bgcolor: 'background.paper', 
                p: 1,
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                Viewing transcription: <strong>{transcription_request_id.substring(0, 8)}...</strong>
              </Typography>
              <Button 
                size="small" 
                variant="outlined" 
                onClick={() => {
                  // Clear the specific transcription view and go back to list
                  window.history.pushState({}, '', '/reports');
                }}
              >
                Clear
              </Button>
            </Box>
          )}
        </Box>
        <TableContainer component={Paper} variant="outlined" sx={{ mb: 0 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ verticalAlign: 'top' }}>Time</TableCell>
                <TableCell sx={{ verticalAlign: 'top' }}>AI Version & Config</TableCell>
                <TableCell sx={{ verticalAlign: 'top' }}>Input</TableCell>
                <TableCell sx={{ verticalAlign: 'top' }}>Score</TableCell>
                <TableCell sx={{ verticalAlign: 'top' }}>Questions & Responses</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {sortedRuns.map((run) => {
                // Calculate subtotals for this run
                const totalQuestions = run.total_questions || 0;
                const answeredQuestions = run.answered_questions_cnt || 0;
                let correct = 0, incorrect = 0, hallucination = 0, rated = 0;
                if (run.answered_questions && run.answered_questions.length > 0) {
                  run.answered_questions.forEach((q) => {
                    const ratingKey = run.id + '-' + q.question_code;
                    const rating = ratingsMap.get(ratingKey);
                    if (rating) {
                      rated++;
                      if (rating.answer_code === 'Correct') correct++;
                      else if (rating.answer_code === 'Incorrect') incorrect++;
                      else if (rating.answer_code === 'Hallucination') hallucination++;
                    }
                  });
                }
                const answerRate = totalQuestions ? (answeredQuestions / totalQuestions) * 100 : 0;
                const accuracy = rated ? (correct / rated) * 100 : 0;
                const hallucinationRate = rated ? (hallucination / rated) * 100 : 0;
                const incorrectRate = rated ? (incorrect / rated) * 100 : 0;
                return (
                  <React.Fragment key={run.id + '-' + run.started}>
                    <TableRow style={{ background: '#f3f4f6' }}>
                      <TableCell colSpan={5}>
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, alignItems: 'center', py: 1 }}>
                          <Chip label={`Answered: ${answeredQuestions}/${totalQuestions} (${answerRate.toFixed(1)}%)`} color="primary" size="small" />
                          <Chip label={`Accuracy: ${accuracy.toFixed(1)}% (${correct})`} color="success" size="small" />
                          <Chip label={`Hallucination: ${hallucinationRate.toFixed(1)}% (${hallucination})`} color="error" size="small" />
                          <Chip label={`Incorrect: ${incorrectRate.toFixed(1)}% (${incorrect})`} color="warning" size="small" />
                        </Box>
                      </TableCell>
                    </TableRow>
                    <TableRow 
                      key={run.id + '-' + run.started}
                      id={`transcription-${run.id}`}
                      ref={el => transcriptionRefs.current[run.id] = el}
                    >
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        {new Date(run.started * 1000).toLocaleString()}<br/><br/>
                        <b>tenant:</b> {run.company_id}<br/>
                        <b>user:</b> {run.user_id}<br/>
                        <b>client:</b> {run.client_id}<br/>
                        <b>visit:</b> {run.visit_id}<br/>
                        <b>assessment:</b> {run.assessment_id}<br/>
                        <b>id:</b> <a 
                          href={`/reports/${run.id}`} 
                          onClick={(e) => {
                            e.preventDefault();
                            // Copy to clipboard when Ctrl/Cmd is pressed
                            if (e.ctrlKey || e.metaKey) {
                              copyTranscriptionLink(run.id);
                            } else {
                              // Update URL without full reload
                              window.history.pushState({}, '', `/reports/${run.id}`);
                              scrollToTranscription(run.id);
                            }
                          }}
                          style={{ 
                            textDecoration: 'none', 
                            color: '#2563eb',
                            cursor: 'pointer',
                            fontSize: '0.9em'
                          }}
                          title="Click to navigate to this transcription, Ctrl+Click to copy link"
                        >
                          {run.id.substring(0, 8)}...
                        </a>

                        <br/>
                        <div style={{ display: 'flex', gap: '8px', marginTop: '4px' }}>
                          <button
                            style={{
                              background: '#ffffff',
                              color: '#6366F1',
                              border: '1px solid #6366F1',
                              borderRadius: 4,
                              padding: '4px 12px',
                              fontWeight: 600,
                              fontSize: '0.95em',
                              cursor: 'pointer',
                              boxShadow: '0 1px 2px rgba(99,102,241,0.08)',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 6
                            }}
                            type="button"
                            onClick={() => copyTranscriptionLink(run.id)}
                            title="Copy a direct link to this transcription"
                          >
                            copy link
                          </button>
                          {/*}
                          <button
                            style={{
                              background: '#ffffff',
                              color: '#6366F1',
                              border: '1px solid #6366F1',
                              borderRadius: 4,
                              padding: '4px 12px',
                              fontWeight: 600,
                              fontSize: '0.95em',
                              cursor: 'pointer',
                              boxShadow: '0 1px 2px rgba(99,102,241,0.08)',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 6
                            }}
                            type="button"
                            onClick={() => handleExportAssessment(run)}
                            title="Export this assessment as JSON"
                          >
                            <Download size={16} style={{ marginRight: 4 }} /> export
                          </button>
                          */}
                        </div>
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        <b>vendor:</b>{run.ai_service_type}<br/>
                        <b>model:</b>{run.ai_service_model}<br/> 
                        <b>temp:</b>{run.ai_temperature}<br/> 
                        <b>ver:</b>{run.version}</TableCell>
                      <TableCell sx={{ verticalAlign: 'top'}}>
                        <b>service:</b> {run.transcription_service_type}<br/>
                        <b>model:</b> {run.transcription_service_model}<br/>
                        <b>question file:</b> {run.question_files}<br/>
                        <b>size:</b> {Math.round(run.audio_buffer_size/1024)}KB<br/>
                        {run.audio_files && run.audio_files.length > 0 && (
                          <div style={{ marginTop: 8 }}>
                            <b>audio:</b> {`${run.audio_files.join(', ')}`}
                            <div>
                              {run.audio_files.map((file, idx) => {
                                const key = `${run.id}-${idx}`;
                                const isShown = jsonDataMap[key] !== undefined;
                                return (
                                  <div key={file + idx} style={{ marginTop: 4 }}>
                                    <audio controls style={{ width: '100%' }}>
                                      <source src={file} />
                                      Your browser does not support the audio element.
                                    </audio>
                                    <div>
                                      <Button
                                        variant="outlined"
                                        size="small"
                                        sx={{ mt: 1, fontSize: '0.95em', textTransform: 'none' }}
                                        onClick={async () => {
                                          if (isShown) {
                                            setJsonDataMap(prev => ({ ...prev, [key]: undefined }));
                                            return;
                                          }
                                          setJsonLoadingMap(prev => ({ ...prev, [key]: true }));
                                          try {
                                            const data = await apiClient(
                                              `file-path/${run.company_id}/${run.visit_id}/${run.assessment_id}/output/conversation.json`,
                                              { method: 'GET' },
                                              true
                                            );
                                            setJsonDataMap(prev => ({ ...prev, [key]: data }));
                                          } catch (error) {
                                            console.error('Failed to load conversation:', error);
                                            setJsonDataMap(prev => ({ ...prev, [key]: { error: 'Failed to load JSON' } }));
                                          } finally {
                                            setJsonLoadingMap(prev => ({ ...prev, [key]: false }));
                                          }
                                        }}
                                        disabled={jsonLoadingMap[key]}
                                      >
                                        {jsonLoadingMap[key]
                                          ? 'Loading...'
                                          : isShown
                                          ? 'Hide Conversation'
                                          : 'Show Conversation'}
                                      </Button>
                                      {isShown && (
                                        <Box sx={{ mt: 1, p: 1, borderRadius: 1, fontSize: '0.95em' }}>
                                          {Array.isArray((jsonDataMap[key] as { data?: Array<{ speaker_id: number; text: string }> }).data)
                                            ? ((jsonDataMap[key] as { data: Array<{ speaker_id: number; text: string }> }).data).map((item, i) => {
                                                const annotationKey = `${run.id}|${i}`;
                                                const annotation = commentAnnotationsMap.get(annotationKey);
                                                return (
                                                  <div key={i} style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                                                    <div style={{ flex: 1 }}>
                                                      <b>speaker {item.speaker_id}:</b> {item.text}
                                                      {annotation && (
                                                        <span style={{ color: '#6366F1', marginLeft: 8 }}>
                                                          <b>Annotation:</b> {annotation.annotation}
                                                        </span>
                                                      )}
                                                    </div>
                                                    <Button
                                                      size="small"
                                                      variant="text"
                                                      sx={{ minWidth: 0, ml: 1, color: '#6366F1' }}
                                                      onClick={() => handleOpenAnnotationDialog(run, i, annotation ?? null)}
                                                      title={annotation ? 'Edit annotation' : 'Add annotation'}
                                                    >
                                                      ✏️
                                                    </Button>
                                                  </div>
                                                );
                                              })
                                            : (
                                              <span>
                                                {typeof jsonDataMap[key] === 'object' ? JSON.stringify(jsonDataMap[key], null, 2) : String(jsonDataMap[key])}
                                              </span>
                                            )}
                                        </Box>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top' }}>
                        <b>answered:</b> {run.answered_questions_cnt}/{run.total_questions}&nbsp;&nbsp;
                        {run.total_questions
                          ? `${Math.round((run.answered_questions_cnt / run.total_questions) * 100)}%`
                          : '0%'}<br/>
                        <b>accuracy:</b> ???<br/>
                        <b>time:</b> {`${run.total_time}s`}<br/>
                      </TableCell>
                      <TableCell sx={{ verticalAlign: 'top', whiteSpace: 'pre-line' }}>
                        {run.answered_questions && run.answered_questions.length > 0 ? (
                          run.answered_questions.map((q: AnsweredQuestion, qidx: number) => (
                            <Box key={q.question_code + '-' + qidx} sx={{ mb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600, flex: 1 }}>{q.question_code}: {q.question_text}</Typography>
                                {(() => {
                                  const ratingKey = run.id + '-' + q.question_code;
                                  const existingRating = ratingsMap.get(ratingKey);
                                  if (existingRating) {
                                    return (
                                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 0.5 }}>
                                        <Chip
                                          label={existingRating.answer_code}
                                          color={
                                            existingRating.answer_code === 'Correct' 
                                              ? 'success' 
                                              : existingRating.answer_code === 'Incorrect'
                                              ? 'warning'
                                              : 'error'
                                          }
                                          size="small"
                                          sx={{ 
                                            fontSize: '0.75rem', 
                                            height: 22,
                                            cursor: 'pointer',
                                            '&:hover': {
                                              boxShadow: '0px 1px 3px rgba(0,0,0,0.2)'
                                            }
                                          }}
                                          onClick={() => handleEditRating(run, q, existingRating)}
                                        />
                                        {existingRating.comment && (
                                          <Typography 
                                            variant="caption" 
                                            color="text.secondary" 
                                            sx={{ 
                                              fontSize: '0.7rem', 
                                              maxWidth: 200, 
                                              textAlign: 'right', 
                                              cursor: 'pointer',
                                              '&:hover': { textDecoration: 'underline' }
                                            }}
                                            onClick={() => handleEditRating(run, q, existingRating)}
                                          >
                                            {existingRating.comment}
                                          </Typography>
                                        )}
                                        <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                                          by {existingRating.admin_id}
                                        </Typography>
                                      </Box>
                                    );
                                  } else {
                                    return (
                                      <>
                                        <Button
                                          variant="outlined"
                                          color="success"
                                          size="small"
                                          sx={{ textTransform: 'none', fontWeight: 600, minWidth: 0, px: 1, py: 0, fontSize: '0.75rem' }}
                                          onClick={() => handleProcessQuestion('Correct', run, q)}
                                        >
                                          Correct
                                        </Button>
                                        <Button
                                          variant="outlined"
                                          color="warning"
                                          size="small"
                                          sx={{ textTransform: 'none', fontWeight: 600, minWidth: 0, px: 1, py: 0, fontSize: '0.75rem' }}
                                          onClick={() => handleProcessQuestion('Incorrect', run, q)}
                                        >
                                          Incorrect
                                        </Button>
                                        <Button
                                          variant="outlined"
                                          color="error"
                                          size="small"
                                          sx={{ textTransform: 'none', fontWeight: 600, minWidth: 0, px: 1, py: 0, fontSize: '0.75rem' }}
                                          onClick={() => handleProcessQuestion('Hallucination', run, q)}
                                        >
                                          Hallucination
                                        </Button>
                                      </>
                                    );
                                  }
                                })()}
                              </Box>
                              {q.answer_text && q.answer_text.length > 0 && (
                                <Typography variant="body2" color="primary" sx={{ ml: 1 }}>
                                  {q.answer_text.join('\n')}
                                </Typography>
                              )}
                              {q.answer_context && q.answer_context.length > 0 && (
                                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                                  {q.answer_context.join('\n')}
                                </Typography>
                              )}
                            </Box>
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary">No answers</Typography>
                        )}
                      </TableCell>
                    </TableRow>
                  </React.Fragment>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Rating Dialog */}
      <Dialog open={ratingDialog.open} onClose={handleCancelRating} maxWidth="sm" fullWidth>
        <DialogTitle>
          {ratingDialog.type && (
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography component="span">
                {(() => {
                  const ratingKey = ratingDialog.run?.id + '-' + ratingDialog.question?.question_code;
                  const isEditing = ratingKey && ratingsMap.has(ratingKey);
                  return isEditing ? `Edit Rating (${ratingDialog.type})` : `Rate as ${ratingDialog.type}`;
                })()}
              </Typography>
              {(() => {
                const ratingKey = ratingDialog.run?.id + '-' + ratingDialog.question?.question_code;
                const isEditing = ratingKey && ratingsMap.has(ratingKey);
                return isEditing && (
                  <Chip 
                    label={ratingDialog.type} 
                    size="small"
                    color={
                      ratingDialog.type === 'Correct' 
                        ? 'success' 
                        : ratingDialog.type === 'Incorrect'
                        ? 'warning'
                        : 'error'
                    }
                  />
                );
              })()}
            </Box>
          )}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {ratingDialog.question && `${ratingDialog.question.question_code}: ${ratingDialog.question.question_text}`}
            </Typography>
            
            {/* Rating type selector */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>Rating Type</Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant={ratingDialog.type === 'Correct' ? 'contained' : 'outlined'}
                  color="success"
                  size="small"
                  onClick={() => setRatingDialog(prev => ({ ...prev, type: 'Correct' }))}
                  sx={{ minWidth: '100px' }}
                >
                  Correct
                </Button>
                <Button
                  variant={ratingDialog.type === 'Incorrect' ? 'contained' : 'outlined'}
                  color="warning"
                  size="small"
                  onClick={() => setRatingDialog(prev => ({ ...prev, type: 'Incorrect' }))}
                  sx={{ minWidth: '100px' }}
                >
                  Incorrect
                </Button>
                <Button
                  variant={ratingDialog.type === 'Hallucination' ? 'contained' : 'outlined'}
                  color="error"
                  size="small"
                  onClick={() => setRatingDialog(prev => ({ ...prev, type: 'Hallucination' }))}
                  sx={{ minWidth: '100px' }}
                >
                  Hallucination
                </Button>
              </Box>
            </Box>
            
            {/* Comment text field */}
            <TextField
              autoFocus
              margin="dense"
              label="Comment (optional)"
              type="text"
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              value={ratingComment}
              onChange={(e) => setRatingComment(e.target.value)}
              placeholder="Add any additional notes or comments..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelRating}>Cancel</Button>
          <Button 
            onClick={handleSubmitRating} 
            variant="contained"
            color={
              ratingDialog.type === 'Correct' 
                ? 'success' 
                : ratingDialog.type === 'Incorrect'
                ? 'warning'
                : 'error'
            }
          >
            {(() => {
              const ratingKey = ratingDialog.run?.id + '-' + ratingDialog.question?.question_code;
              const isEditing = ratingKey && ratingsMap.has(ratingKey);
              return isEditing ? 'Update Rating' : `Submit ${ratingDialog.type}`;
            })()}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Annotation Dialog */}
      <Dialog open={annotationDialog.open} onClose={handleCloseAnnotationDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {annotationDialog.existing ? 'Edit Annotation' : 'Add Annotation'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Annotation"
            type="text"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={annotationDialog.annotation}
            onChange={e => setAnnotationDialog(prev => ({ ...prev, annotation: e.target.value }))}
            placeholder="Add your annotation for this dialog..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAnnotationDialog}>Cancel</Button>
          <Button onClick={handleSubmitAnnotation} variant="contained" color="primary">
            {annotationDialog.existing ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ReportsPage;