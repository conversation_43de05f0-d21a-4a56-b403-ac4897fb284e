#!/bin/bash
# install venv
# This script is used to install the ai_core package and its dependencies in a virtual environment.
set -e
# create a virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

source venv/bin/activate
# install the required packages
pip install --upgrade pip
# Use --no-compile to avoid aenum compilation issues with Python 3.9
pip install --no-compile -e .

# move to the packages directory and install the desired packages
cd ../../packages/auth
pip install --no-compile -e .
cd ../database
pip install --no-compile -e .
cd ../queues
pip install --no-compile -e .
cd ../config
pip install --no-compile -e .
