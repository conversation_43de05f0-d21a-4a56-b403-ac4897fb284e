name: Deploy ai_core to AWS EC2

on:
  push:
    branches:
      - main
      - develop

jobs:
  deploy-dev:
    if: github.ref == 'refs/heads/develop'
    runs-on: ubuntu-latest
    steps:
      - name: Set up SSH key for dev
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AWS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "************" >> ~/.ssh/known_hosts

      - name: Deploy on Dev EC2
        run: |
          ssh ubuntu@************ "GITHUB_RUN_NUMBER=${GITHUB_RUN_NUMBER} bash -s" << 'EOF'
            repo_root="/home/<USER>/ai_core"

            cd "$repo_root"
            echo "pulling latest changes from develop..."
            git pull origin develop

            cd "$repo_root/apps/ai_core"
            VERSION="0.1.${GITHUB_RUN_NUMBER}"
            echo "{\"version\":\"$VERSION\",\"commit\":null,\"commit_message\":null,\"tag\":null}" > version.json
            COMMIT_HASH=$(git -C "$repo_root" rev-parse HEAD)
            COMMIT_MESSAGE=$(git -C "$repo_root" log -1 --pretty=%B)
            COMMIT_TAG=$(git -C "$repo_root" describe --tags --abbrev=0 2>/dev/null || echo "none")

            echo "COMMIT_HASH: $COMMIT_HASH"
            echo "COMMIT_MESSAGE: $COMMIT_MESSAGE"
            echo "COMMIT_TAG: $COMMIT_TAG"

            jq --arg v "$VERSION" --arg ch "$COMMIT_HASH" --arg cm "$COMMIT_MESSAGE" --arg t "$COMMIT_TAG" \
               '.version = $v | .commit = $ch | .commit_message = $cm | .tag = $t' version.json > tmp.json && mv tmp.json version.json
            echo "Deployment version: $(cat version.json)"

            cd "$repo_root/apps/ai_core_fe"
            if ! command -v npm >/dev/null 2>&1; then
              echo "npm not found, installing npm@10.9.2..."
              curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
              sudo apt-get install -y nodejs
              sudo npm ci -g npm@10.9.2
            fi
            npm ci
            npm run build
            mv dist build
            rm -rf "$repo_root/apps/ai_core/build"
            mv build "$repo_root/apps/ai_core/"

            cd "$repo_root/apps/ai_core"
            echo "Setting VERSION in docker-compose..."
            export VERSION="0.1.${GITHUB_RUN_NUMBER}"
            echo "Stopping and removing old containers..."
            docker-compose down || true
            echo "Building and starting with docker-compose..."
            docker-compose up -d --build
            echo "Waiting for app to respond..."
            for i in $(seq 1 10); do
              sleep 5
              STATUS=$(curl -k -s -o /dev/null -w '%{http_code}' https://admin-dev.goscribble.ai/docs || echo "000")
              if [[ "$STATUS" == "200" ]]; then
                echo "Dev app is up!"
                cd "$repo_root/apps/ai_core"
                echo "Setting up Python virtual environment..."
                bash install_py.sh
                source venv/bin/activate
                # echo "Running integration tests..."
                # pytest tests/integration/test_scribble_integration.py --disable-warnings -v
                # status=$?
                # if [ $status -ne 0 ]; then
                #   echo "Tests failed"
                #   exit $status
                # fi
                echo "Tests completed successfully"
                exit 0
              fi
              echo "Retry $i: not up yet"
            done
            echo "Dev app failed to start"
            docker-compose logs ai-core
            exit 1
          EOF

      - name: Trigger scribble-testing workflow.
        run: |
          curl -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.TEST_REPO_TOKEN }}" \
            https://api.github.com/repos/Acutedge-Inc/scribble-testing/dispatches \
            -d '{"event_type":"run-build","client_payload":{"source_repo":"${{ github.repository }}","ref":"${{ github.ref }}"}}'          



  deploy-prod:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Set up SSH key for prod
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AWS_SSH_PROD_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "*************" >> ~/.ssh/known_hosts
      - name: Deploy on Prod EC2
        run: |
          ssh ubuntu@************* "GITHUB_RUN_NUMBER=${GITHUB_RUN_NUMBER} bash -s" << 'EOF'
            repo_root="/home/<USER>/ai_core"

            cd "$repo_root"
            echo "pulling latest changes from main..."
            git pull origin main

            cd "$repo_root/apps/ai_core"
            VERSION="0.1.${GITHUB_RUN_NUMBER}"
            echo "{\"version\":\"$VERSION\",\"commit\":null,\"commit_message\":null,\"tag\":null}" > version.json
            COMMIT_HASH=$(git -C "$repo_root" rev-parse HEAD)
            COMMIT_MESSAGE=$(git -C "$repo_root" log -1 --pretty=%B)
            COMMIT_TAG=$(git -C "$repo_root" describe --tags --abbrev=0 2>/dev/null || echo "none")

            echo "COMMIT_HASH: $COMMIT_HASH"
            echo "COMMIT_MESSAGE: $COMMIT_MESSAGE"
            echo "COMMIT_TAG: $COMMIT_TAG"

            jq --arg v "$VERSION" --arg ch "$COMMIT_HASH" --arg cm "$COMMIT_MESSAGE" --arg t "$COMMIT_TAG" \
               '.version = $v | .commit = $ch | .commit_message = $cm | .tag = $t' version.json > tmp.json && mv tmp.json version.json
            echo "Deployment version: $(cat version.json)"

            cd "$repo_root/apps/ai_core_fe"
            if ! command -v npm >/dev/null 2>&1; then
              echo "npm not found, installing npm@10.9.2..."
              curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
              sudo apt-get install -y nodejs
              sudo npm ci -g npm@10.9.2
            fi
            npm ci
            npm run build
            mv dist build
            rm -rf "$repo_root/apps/ai_core/build"
            mv build "$repo_root/apps/ai_core/"

            cd "$repo_root/apps/ai_core"
            echo "Setting VERSION in docker-compose..."
            export VERSION="0.1.${GITHUB_RUN_NUMBER}"
            echo "Stopping and removing old containers..."
            docker-compose down || true
            echo "Building and starting with docker-compose..."
            docker-compose up -d --build
            echo "Waiting for app to respond..."
            for i in $(seq 1 10); do
              sleep 5
              STATUS=$(curl -k -s -o /dev/null -w '%{http_code}' https://admin-prod.goscribble.ai/docs || echo "000")
              if [[ "$STATUS" == "200" || "$STATUS" == "404" ]]; then
                echo "Prod app is up!"
                exit 0
              fi
              echo "Retry $i: not up yet"
            done
            echo "Prod app failed to start"
            docker-compose logs ai-core
            exit 1
          EOF