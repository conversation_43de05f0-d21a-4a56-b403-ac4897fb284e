from pydantic import BaseModel, Field
from typing import List, Any, Optional, Literal
import time
import json
import yaml

class TranscriptionRequest(BaseModel):
    id: Optional[str] = Field(None, description="Transcription Request ID - can be created by client or will be self generated", example="123e4567-e89b-12d3-a456-************")
    user_id: str = Field(None, description="clinician/user id", example="<EMAIL>")
    visit_id: str = Field(None, description="The visit id", example="visit-1234")
    client_id: Optional[str] = Field(None, description="client id - uuid ?", example="client_id-*********")
    assessment_id: Optional[str] = Field(None, description="assessment id - uuid ?", example="assessment-1234")
    company_id: Optional[str] = Field(None, description="root key for data", example="amedysis")
    transcribe_type: Literal["deepgram", "aws-transcribe", "whisper"] = Field("deepgram", description="transcription type", example="deepgram")
    audio_files: list[str] = Field(None, example=["Kate-SOC-Visit-Audio-49min26sec.webm"])
    question_files: list[str] = Field(None, ddescription="questions for this assesment", example=["questionForm-181.json"])
    mode: Literal["all-stages", "answers-only", "transcription-only"] = Field("all-stages", description="mode of processing", example="all-stages")
    status: Optional[str] = Field(None, example="error")
    started: Optional[int] = Field(None, example=1683*********)
    completed: Optional[int] = Field(None, example=1683*********)
    exception: Optional[str] = Field(None, example="An error occurred")
    answer_files: Optional[list[str]] = Field([], example=["answer.json"])
    transcription_files: Optional[list[str]] = Field([], example=["transcription.json"])
    conversation_files: Optional[list[str]] = Field([], example=["conversation.json"])
    total_questions: Optional[int] = Field(None, example=5)
    answered_questions_cnt: Optional[int] = Field(None, example=3)
    answered_questions: Optional[list[Any]] = Field([], example=[])
    total_time: Optional[int] = Field(None, example=120)
    transcription_time: Optional[int] = Field(None, example=60)
    transcription_service_type: Optional[str] = Field(None, example="deepgram")
    transcription_service_model: Optional[str] = Field(None, example="whisper")
    ai_service_type: Optional[str] = Field(None, example="openai")
    ai_service_model: Optional[str] = Field(None, example="gpt-4")
    ai_temperature: Optional[float] = Field(0.0, example=0.5)
    ai_prompt: Optional[str] = Field(None, example="Please answer the following questions.")
    ai_embeddings: Optional[list[str]] = Field(None, example=["embedding1", "embedding2"])
    audio_buffer_size: Optional[int] = Field(0, example=123456)
    version: Optional[str] = Field(None, example="0.1.0")
    commit: Optional[str] = Field(None, example="*********0")

    @classmethod
    def from_yaml(cls, yaml_str: str):
        """Load TranscriptionRequest from a YAML string."""
        data = yaml.safe_load(yaml_str)
        return cls(**data)

    @classmethod
    def from_yaml_file(cls, file_path: str):
        """Load TranscriptionRequest from a YAML file."""
        with open(file_path, "r") as file:
            return cls.from_yaml(file.read())

    @classmethod
    def from_json(cls, json_str: str):
        """Load TranscriptionRequest from a JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    @classmethod
    def from_json_file(cls, file_path: str):
        """Load TranscriptionRequest from a JSON file."""
        with open(file_path, "r") as file:
            return cls.from_json(file.read())