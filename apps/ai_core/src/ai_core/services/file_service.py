# FIXME - Request probably should not be here
# instead individual query parameters should be defined in the app.yml
# then put into a dictionary parameter to the service methods
from fastapi import Request
import logging
from typing import List
import uuid
from config import get_settings
from queues.interface import QueueClient
from database.interface import DatabaseAdapter
from ai_core.models.file import File
from ai_core.service_response import info_message, error_message, warning_message
from pathlib import Path


logger = logging.getLogger(__name__)

# write - Create an item
def create_file(item: File, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============create_file called==============")

    item_id = item.id if hasattr(item, "id") and item.id else str(uuid.uuid4())
    logger.info(f"Using item_id: {item_id}")
    new_item = item.model_dump()
    new_item["id"] = item_id  # Store UUID in the database

    logger.info(item)

    # FIXME - if db: ...
    db.insert_item("file", item_id, new_item)
    logger.info(f"File created: {new_item}")
    if q:
        q.send_message(new_item)
        logger.info(f"Message sent to queue: File created: {new_item}")
        logger.info(f"Queue message count: {q.get_message_count()}")
    return new_item

# read - get all items
def get_all_file(db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_all_file called==============")
    return db.get_all_items("file")

# read - get an item
def get_file(id: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_file called==============")
    logger.info(f"Received request to retrieve file with id: {id}")
    item = db.get_item("file", id)
    return item

# read - get_path an item
def get_path_file(path: str, db: DatabaseAdapter, user: dict, request: Request):
    logger.info("===============get_file called==============")
    logger.info(f"Received request to retrieve {path} with path: {path}")
    # item = db.get_item("file", path)
    # return item
    # settings = get_settings()
    table = path.split("/")[0]
    key = "/".join(path.split("/")[1:])
    item = db.get_item(table, key)
    # if not item:
    #     raise ValueError(f"Item not found: {path}")    
    return {"data": item}



# write - update an item (without modifying ID)
def update_file(id: str, new_item: File, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============update_file called==============")
    logger.info(new_item)
    db.update_item("file", id, new_item.model_dump())
    return db.get_item("file", id)

# write - delete an item
def delete_file(id: str, db: DatabaseAdapter, q: QueueClient, user: dict, request: Request):
    logger.info("===============delete_file called==============")
    logger.info(f"Received request to delete file with id {id}")
    item = db.get_item("file", id)
    if not item:
        logger.warning(f"File with id {id} not found")
        return None
    db.delete_item("file", id)
    return item

