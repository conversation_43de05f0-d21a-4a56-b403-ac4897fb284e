import logging
import hashlib
import secrets
import os
import jwt
from datetime import datetime, timedelta, timezone
from typing import Optional
from threading import Lock
from .interface import Auth<PERSON><PERSON>ider
from database.interface import DatabaseAdapter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

JWT_SECRET_KEY = "your_secret_key"  # Replace with a secure key
JWT_EXPIRATION_MINUTES = 60  # Token expiration time
USERS_TABLE = "user"


from typing import Dict, Callable
from threading import Lock
from database.interface import DatabaseAdapter
class LocalAuthProvider(AuthProvider):
    """Local authentication provider (uses DatabaseAdapter for persistence, JWT for tokens)."""

    _instance = None
    _lock = Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:  # Double-checked locking
                    # Call the parent __new__ method without passing args/kwargs
                    cls._instance = super(LocalAuthProvider, cls).__new__(cls)
        return cls._instance

    def __init__(self, config: Dict[str, str], database_factory: Callable[[Callable[[], Dict[str, str]]], DatabaseAdapter]):
        """
        Initialize the local authentication provider.

        :param config: The configuration dictionary.
        :param database_factory: A callable that provides a DatabaseAdapter instance.
        """
        if hasattr(self, "_initialized") and self._initialized:
            return
        self._initialized = True

        # Use the database factory to get a DatabaseAdapter instance
        self.database = database_factory(lambda: config)

        # Initialize users in the database
        self._initialize_users()

    # WARNING - to have a form of authentication we need a concept of users and roles for authorization
    def _initialize_users(self):
        if not self.database:
            logger.warning("There does not appear to be a database configured. No users nor authentication will be available.")
            return
        """Ensure the users table exists and create a default Admin user if empty."""
        if not self.database.get_all_items(USERS_TABLE):
            logger.info("Initializing default Admin user.")
            admin_password = self._get_or_create_admin_password()
            admin_password_hash = self.generate_hash(admin_password)            
            self.database.insert_item(
                USERS_TABLE,
                "<EMAIL>",
                {
                    "id": "<EMAIL>",
                    "email": "<EMAIL>",
                    "password_hash": admin_password_hash,
                    "roles": ["Admin"],
                    "modified": int(datetime.now(timezone.utc).timestamp() * 1000),
                },
            )

    def _generate_jwt(self, username: str) -> str:
        """Generate a JWT for the given username."""
        if not username:
            raise ValueError("_generate_jwt Username cannot be empty")

        username = username.lower()

        user = self.database.get_item(USERS_TABLE, username)
        if user:
            user.pop("password_hash", None)
            user.pop("password", None)        
            user.pop("token", None)        

        payload = {
            "sub": username,
            "exp": datetime.now(timezone.utc) + timedelta(minutes=JWT_EXPIRATION_MINUTES),
            "user": user
        }
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm="HS256")
        return token if isinstance(token, str) else token.decode("utf-8")

    def _verify_jwt(self, token: str) -> Optional[dict]:
        """Verify a JWT and return its payload if valid."""
        try:
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=["HS256"])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT has expired")
        except jwt.InvalidTokenError:
            logger.warning("Invalid JWT")
        return None
    
    def _get_or_create_admin_password(self):
        PASSWORD_FILE = "admin_password.txt"
        if os.path.exists(PASSWORD_FILE):
            with open(PASSWORD_FILE, "r") as file:
                password = file.read().strip()
            print("Admin password loaded from file.")
        else:
            password = secrets.token_urlsafe(16)  # Generates a secure random password
            with open(PASSWORD_FILE, "w") as file:
                file.write(password)
            print("New admin password generated and saved.")
        
        return password    

    # FIXME - change username to id
    # Deprecated - since the whole registration object should be passed, and the details handled in a "per app" way
    def register_user(self, username: str, password: str = None, password_hash: str = None) -> dict:
        if password_hash is None and password is not None:
            raise ValueError("register_user Password hash or password is required when registering a user")
        if password_hash is not None and password is not None:
            raise ValueError("register_user Password hash and password cannot be provided at the same time")
        
        username = username.lower()

        if self.database.get_item(USERS_TABLE, username):
            raise ValueError(f"register_user user already exists {username}")
        if password_hash is None:
            password_hash = self.generate_hash(password)
        self.database.insert_item(
            USERS_TABLE,
            username,
            {
                "id": username,
                "email": username,
                "password_hash": password_hash,
                "roles": [],
                "modified": int(datetime.now(timezone.utc).timestamp() * 1000),
            },
        )
        return {"username": username, "message": "User registered"}

    def authenticate(self, username: str, password: str) -> Optional[str]:
        logger.info(f"Authenticating user: {username}")
        if not username:
            raise ValueError("authenticate username cannot be empty")

        username = username.lower()

        user = self.database.get_item(USERS_TABLE, username)
        if user:
            user["login_count"] = (user.get("login_count") or 0) + 1
            self.database.update_item(USERS_TABLE, username, user)
            if user["password_hash"] == self.generate_hash(password):
                token = self._generate_jwt(username)
                user["token"] = token
                user["last_login"] = int(datetime.now(timezone.utc).timestamp() * 1000)
                self.database.update_item(USERS_TABLE, username, user)
                return token
            else:
                self.database.update_item(USERS_TABLE, username, {"last_unsuccessful_login": int(datetime.now(timezone.utc).timestamp() * 1000)})
        return None

    def get_user(self, token: str) -> Optional[dict]:
        payload = self._verify_jwt(token)
        if payload:
            username = payload.get("sub")
            if not username:
                raise ValueError("get_user sub username cannot be empty")
            username = username.lower()
            user = self.database.get_item(USERS_TABLE, username)
            # DEBUG LOGGING
            logger.info(f"[AUTH DEBUG] DB path: {getattr(self, 'sqlite_path', getattr(self.database, 'sqlite_path', 'unknown'))}")
            logger.info(f"[AUTH DEBUG] Looking up user: {username}")
            logger.info(f"[AUTH DEBUG] User record: {user}")
            logger.info(f"[AUTH DEBUG] Token in DB: {user.get('token') if user else None}")
            logger.info(f"[AUTH DEBUG] Token in request: {token}")
            if user:
                return {"id": user["id"], "email": user["email"], "roles": user.get("roles", [])}
        return None

    def refresh_token(self, refresh_token: str) -> Optional[str]:
        """Generate a new token if the refresh token is valid."""
        payload = self._verify_jwt(refresh_token)
        if payload:
            username = payload.get("sub")
            if not username:
                raise ValueError("refresh_token sub username cannot be empty")
            username = username.lower()
            user = self.database.get_item(USERS_TABLE, username)
            if user:
                new_token = self._generate_jwt(username)
                user["token"] = new_token
                user["modified"] = int(datetime.now(timezone.utc).timestamp() * 1000)
                self.database.update_item(USERS_TABLE, username, user)
                logger.info(f"Token refreshed for user {username}.")
                return new_token
        logger.warning("Invalid refresh token provided.")
        return None

    def logout(self, token: str) -> bool:
        """Log out a user by invalidating their token."""
        payload = self._verify_jwt(token)
        if payload:
            username = payload.get("sub")
            if not username:
                raise ValueError("logout username cannot be empty")
            username = username.lower()
            user = self.database.get_item(USERS_TABLE, username)
            if user:
                user["token"] = None
                user["modified"] = int(datetime.now(timezone.utc).timestamp() * 1000)
                self.database.update_item(USERS_TABLE, username, user)
                logger.info(f"User {username} logged out successfully.")
                return True
        logger.warning("Invalid token provided for logout.")
        return False

    def generate_token(self, user: dict) -> str:
        """Generate a JWT token for the given user."""
        if not user:
            raise ValueError("generate_token User cannot be empty")
        if not user.get("id"):
            raise ValueError("generate_token User ID cannot be empty")
        if not user.get("email"):
            raise ValueError("generate_token User email cannot be empty")
        id = user["id"].lower()
        email = id
        payload = {
            "sub": id,
            "exp": datetime.now(timezone.utc) + timedelta(minutes=JWT_EXPIRATION_MINUTES),
            "user": {
                "id": id,
                "email": email,
                "roles": user.get("roles", [])
            }
        }
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm="HS256")
        return token if isinstance(token, str) else token.decode("utf-8")
