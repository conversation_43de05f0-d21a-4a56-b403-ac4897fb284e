[build-system]
requires = ["setuptools>=61.0", "wheel", "setuptools_scm"]
build-backend = "setuptools.build_meta"

[project]
name = "auth"
version = "0.1.0"
description = "A Python library for auth"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10"

dependencies = [
    "boto3",
    "pyotp",
    "qrcode[pil]",
    "python-jose",
    "PyJWT"
]

[project.optional-dependencies]
dev = ["pytest"]

[tool.setuptools.packages.find]
where = ["src"]

[tool.pytest.ini_options]
testpaths = ["tests"]