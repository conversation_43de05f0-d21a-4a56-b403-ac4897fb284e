[build-system]
requires = ["setuptools>=61.0", "wheel", "setuptools_scm"]
build-backend = "setuptools.build_meta"

[project]
name = "database"
version = "0.1.0"
description = "A database library for managing data in CloudSeeder"
authors = [{ name = "GroGBot", email = "<EMAIL>" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.9.6"

dependencies = [
    "tinydb",
    "boto3"
]

[tool.setuptools]
package-dir = {"" = "src"} 

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
database = ["*.json"]

[project.optional-dependencies]
dev = ["pytest", "black", "mypy"]
